import React from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import CalliperSEO from '../components/seo';
import Layout from '../components/layout';
import I18n from '../../../../common/components/i18n/i18n';
import Log from '../../../../common/components/log/log';
import { getEnvVariables } from '../../../../common/utils/env';
import '../styles/log.less';

const LogPage = () => {
  const {
    allStrapiCalliperChangelog: { nodes },
  } = useStaticQuery(query);
  const { isVipSite } = getEnvVariables();

  return (
    <Layout className="calliper-log-page" isVipSite={isVipSite} hideLog>
      <Log nodes={nodes}></Log>
    </Layout>
  );
};

export const Head = () => <CalliperSEO />;

export const query = graphql`
  {
    allStrapiCalliperChangelog(sort: { publishAt: DESC }) {
      nodes {
        id
        title
        publishAt
        content {
          data {
            content
            childMarkdownRemark {
              html
              rawMarkdownBody
              frontmatter {
                version
                date
              }
            }
          }
          medias {
            alternativeText
            file {
              alternativeText
            }
            localFile {
              childImageSharp {
                gatsbyImageData
              }
            }
            src
            url
          }
        }
        videos {
          localFile {
            publicURL
          }
        }
      }
    }
  }
`;

const LogZhPage = () => {
  return (
    <I18n>
      <LogPage />
    </I18n>
  );
};

export default LogZhPage;
