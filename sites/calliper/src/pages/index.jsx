import React, { useState, useCallback, useEffect } from 'react';
import <PERSON>iperSEO from '../components/seo';
import I18n from '../../../../common/components/i18n/i18n';
import Layout from '../components/layout';
import Banner from '../components/banner/banner';
import BannerButtons from '../components/banner/banner-buttons';
import BannerButtonsVip from '../components/banner/banner-buttons-vip';
import VipLogin from '../components/vip-login';
import PersonalLogin from '../components/personal-login';
import Demo from '../components/demo';
import Advantage from '../components/advantage';
import ApplicationScenes from '../components/application-scenes';
import Comparison from '../components/comparison';
import ApiTrial from '../components/api-trial';
import Customers from '../../../../common/components/customers/customersSwiper/customers-swiper';
import ConnectWrapper from '../../../../common/components/connect/connectWrapper/connect-wrapper';
import ConnectSideToolbar from '../../../../common/components/connect/connectSideToolbar/connect-side-toolbar';
import GitInfo from '../../../../common/components/gitInfo/git-info';
import { useMobilePageContext } from '../../../../common/hooks/useMobilePageContext';
import classnames from 'classnames';

import { getWechatLoginUserInfo, calliperUserInfo } from '../api/index';
import { CALLIPER_CONNECT_DATA } from '../../../../common/data/connect-wrapper';
import { getUrlWithProduct } from '../../../../common/urls';
import { getEnvVariables } from '../../../../common/utils/env';
import '../styles/index.less';
import '../styles/vip-index.less';
import { graphql, useStaticQuery } from 'gatsby';
import { useGetUrlQueryData } from '../../../../common/hooks/useUrlQueryHook';
import { getConfigs } from '../api/index';
import { ConfigContext } from '../hooks/config-context';

const IndexPage = () => {
  const { gitCommit, swiperData } = useStaticQuery(graphql`
    query {
      gitCommit: gitCommit(latest: { eq: true }) {
        hash
        date
      }
      swiperData: allFile(
        filter: { relativePath: { regex: "/common/white-customers/" } }
        sort: { name: ASC }
      ) {
        nodes {
          childImageSharp {
            gatsbyImageData(layout: CONSTRAINED, width: 300)
          }
          name
        }
      }
    }
  `);

  const { product, isVipSite } = getEnvVariables();
  const isMobile = useMobilePageContext();
  const calliperHref = getUrlWithProduct('calliper', 'index', true);
  const calliperVipHref = getUrlWithProduct('calliper-vip', 'index', true);
  const demoIframeSrc = getUrlWithProduct(product, 'demo');

  const [isLogin, setIsLogin] = useState(true);
  const [userInfo, setUserInfo] = useState(null);
  const [isShowLogin, setIsShowLogin] = useState(false);
  const redirect = useGetUrlQueryData('redirect');
  const [config, setConfig] = useState(null);

  const layoutClass = classnames({
    'calliper-index-page': true,
    'calliper-vip-index-page': isVipSite,
  });

  const gotoHomePage = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('redirect')) {
      window.location.href = `${
        window.location.origin
      }/calliper/#${urlParams.get('redirect')}`;
    } else {
      window.location.href = window.location.origin + '/calliper/';
    }
  }, []);

  const checkLoginStatus = useCallback(async () => {
    try {
      if (isVipSite) {
        const { data } = await calliperUserInfo();
        setUserInfo(data);
      } else {
        await getWechatLoginUserInfo();
      }

      setIsLogin(true);
    } catch (error) {
      setIsLogin(false);
    }
  }, [isVipSite]);

  const getConfig = useCallback(async () => {
    try {
      const { data: configData } = await getConfigs();
      setConfig(configData);
    } catch (error) {
      return null;
    }
  }, []);

  useEffect(() => {
    getConfig();
  }, [getConfig]);

  useEffect(() => {
    checkLoginStatus();
  }, [checkLoginStatus]);

  useEffect(() => {
    if (redirect && !isMobile) {
      setIsShowLogin(true);
    }
  }, [redirect, isMobile]);

  return (
    <ConfigContext.Provider value={config || {}}>
      <>
        <Layout className={layoutClass} isVipSite={isVipSite}>
          <Banner
            calliperLink={isVipSite ? calliperVipHref : calliperHref}
            subtitle={isVipSite ? '文件比对，精准识别差异' : '精确比对文档差异'}
            isShowLogin={isShowLogin}
            bannerButtons={
              isVipSite ? (
                <BannerButtonsVip
                  setIsShowLogin={setIsShowLogin}
                  isShowLogin={isShowLogin}
                />
              ) : (
                <BannerButtons
                  setIsShowLogin={setIsShowLogin}
                  isShowLogin={isShowLogin}
                />
              )
            }
            bannerLogin={
              isVipSite ? (
                <VipLogin
                  isLogin={isLogin}
                  userInfo={userInfo}
                  handleLogin={gotoHomePage}
                />
              ) : (
                <PersonalLogin
                  isShowLogin={isShowLogin}
                  handleLogin={gotoHomePage}
                />
              )
            }
          />
          {!isMobile && (
            <Demo isShowLogin={isShowLogin} demoIframeSrc={demoIframeSrc} />
          )}
          <Advantage />
          <ApplicationScenes />
          <Comparison />
          <ApiTrial />
          <Customers showCustomersCaseTitle data={{ swiperData }} />
          <ConnectWrapper
            connectData={CALLIPER_CONNECT_DATA}
            hidePersonalButton={isMobile}
          />
          <ConnectSideToolbar />
        </Layout>
        <GitInfo data={gitCommit} />
      </>
    </ConfigContext.Provider>
  );
};

export const Head = () => <CalliperSEO />;

const IndexZhPage = () => {
  return (
    <I18n>
      <IndexPage />
    </I18n>
  );
};

export default IndexZhPage;
