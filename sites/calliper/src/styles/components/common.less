.container {
  padding-right: 0;
  padding-left: 0;
}

.common-title {
  margin-bottom: 60px;
  color: @dark-title-color;
  font-weight: 500;
  font-size: 36px;
  line-height: 50px;
  letter-spacing: 2px;
  text-align: center;
}

.header-wrapper .nav-menu .version-links {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 10px 0 10px 15px;
  padding-left: 15px;
  border-left: 1px solid #eaedf3;

  > div {
    margin: 0 8px;

    .nav-link {
      margin: 0;
      padding: 0;

      &:hover {
        background: transparent;
      }
    }
  }

  .active {
    position: relative;

    &::after {
      position: absolute;
      bottom: -10px;
      width: 100%;
      height: 2px;
      background-image: linear-gradient(90deg, #1e9aff, #67c6ff 99%);
      border-radius: 2px;
      content: '';
    }
  }
}

@media (min-width: @max-screen-size) {
  .container {
    max-width: 1460px !important;
  }
}

@media (max-width: @small-screen-size) {
  .common-title {
    font-size: 28px;
    line-height: 32px;
  }
}

@media (max-width: @mini-screen-size) {
  .container {
    padding-right: 20px;
    padding-left: 20px;
  }

  .common-title {
    margin-bottom: 30px;
    font-size: 24px;
    line-height: 34px;
  }
}
