.comparison-wrapper {
  padding: 120px 0;
  background: url('../../assets/images/concentric-circles.png') center no-repeat;
  background-size: cover;
  border-bottom: @border-base;

  .comparison-title {
    margin-bottom: 90px;
  }

  .comparison-content {
    width: 75%;
    margin: 0 auto;
  }

  .comparison-banner,
  .list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .banner-title,
  .list-type {
    display: flex;
    align-items: flex-start;
    width: 48%;
  }

  .banner-title-product,
  .list-product-availability {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 40%;
  }

  .comparison-banner {
    height: 34px;
    margin-bottom: 60px;
  }

  .banner-title-text {
    color: @dark-title-color;
    font-size: 24px;
    line-height: 34px;
    letter-spacing: 0.48px;
  }

  .banner-title-calliper {
    position: relative;
    padding: 3px 10px 4px 9px;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(
        144deg,
        rgba(2, 140, 245, 90%) 0%,
        rgba(4, 251, 250, 90%) 100%
      );
      border-radius: @border-radius-base;
      opacity: 0.3;
      content: '';
    }
  }

  .comparison-lists {
    display: flex;
    flex-direction: column;
    row-gap: 30px;
  }

  .comparison-list {
    padding: 30px;
    background-color: rgba(32, 170, 245, 5%);
    border: 1px solid rgba(32, 170, 245, 20%);
    border-radius: 8px;
    backdrop-filter: blur(5px);
  }

  .list-title,
  .list-item {
    margin-bottom: 30px;
  }

  .list-title-text {
    color: @dark-title-color;
    font-size: 20px;
    line-height: 28px;
    letter-spacing: 0.32px;
  }

  .list-item {
    &:last-child {
      margin-bottom: 0;
    }
  }

  .list-type-text {
    color: @dark-text-color;
    font-size: 18px;
    line-height: 25px;
  }

  .product-availability-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45%;
  }

  .availability-icon-box {
    cursor: pointer;
  }

  .availability-icon {
    width: 24px;
    height: 24px;
  }
}
