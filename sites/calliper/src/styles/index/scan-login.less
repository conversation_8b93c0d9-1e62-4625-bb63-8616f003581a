.scan-login {
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  .scan-login-container {
    z-index: 1;
    width: 350px;
    padding: 30px 0;
    text-align: center;
    background: #fff;
    border: 1px solid #eaedf3;
    border-radius: 4px;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 10%);
  }

  .scan-login-title {
    font-size: 18px;
    text-align: center;
  }

  .login-title-logo {
    width: 31px;
    height: 26px;
    margin-right: 10px;
  }

  .qr-content {
    position: relative;
    width: 210px;
    height: 210px;
    margin: 15px auto;
  }

  .qr-expired {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    background-color: rgba(white, 0.9);
    cursor: pointer;
    row-gap: 10px;
  }

  .qr-info {
    color: #000;
  }

  .qr-svg {
    width: 20px;
    height: 20px;
  }

  .login-qr {
    width: 100%;
    height: 100%;
  }

  .qr-desc {
    i {
      color: #19ad1a;
      font-weight: 600;
    }
  }

  .qr-text-error,
  .qr-spinner {
    line-height: 230px;
  }

  .wechat-tips-img {
    width: 104px;
    margin: 90px 0;
  }

  .agreement-checkbox {
    justify-content: center;
  }

  .user-info-wrap {
    padding: 45px 0;
  }

  .user-personal-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .user-personal-avatar-wrap {
    width: 100px;
    height: 100px;
    border-radius: 50%;
  }

  .user-personal-avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  .user-personal-name {
    margin-top: 19px;
    color: #303133;
    font-size: 16px;
  }

  .scan-footer {
    text-align: center;
  }

  .redirect-button {
    display: block;
    width: 100%;
    max-width: 290px;
    height: 40px;
    margin: auto;
    color: #fff;
    font-size: 14px;
    line-height: 40px;
    background: #6757e5;
    background-image: linear-gradient(
        224.72deg,
        rgba(4, 251, 250, 90%) -1%,
        rgba(2, 140, 245, 90%) 100.5%
      ),
      linear-gradient(
        44.72deg,
        rgba(68, 129, 235, 20%) -0.5%,
        rgba(4, 190, 254, 20%) 101%
      );
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
}
