.demo-wrapper {
  padding: 90px 0 120px;
  border-bottom: @border-base;

  .demo-content-box {
    display: flex;
    justify-content: center;
  }

  .content-bg {
    position: relative;
    display: flex;
    justify-content: center;
    padding: 1.5vw 9vw 3vw;
    background: url('../../../../../common/assets/images/mac-base.png')
      no-repeat;
    background-size: 100% 100%;
  }

  .content-demo {
    width: 58vw;
    height: 33vw;
    border: @border-base;
    border-radius: 5px;
  }

  .content-demo-loading {
    position: absolute;
    bottom: 15px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .content-demo-spinner {
    width: 28px;
    height: 28px;
    border: 2px solid #20aaf5;
    border-right-color: transparent;
  }
}
