.application-scenes-wrapper {
  position: relative;
  padding: 120px 0;
  border-bottom: @border-base;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    opacity: 0.6;
    content: '';
  }

  .application-scenes-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 75%;
    margin: 0 auto;
    row-gap: 4vw;
  }

  .application-scenes-card {
    display: flex;
    justify-content: center;
    width: 305px;
    height: 196px;
    padding: 0;
    border-radius: 40px;
    box-shadow: 0 20px 38px rgba(101, 122, 147, 10%);
    cursor: pointer;

    &:hover {
      .card-content {
        padding: 0 30px;
      }

      .content-title-box {
        display: none;
      }

      .content-desc {
        display: block;
      }
    }
  }

  .content-title-box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .content-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }

  .content-title {
    color: @dark-text-color;
    font-weight: 500;
    font-size: 20px;
    line-height: 42px;
  }

  .content-desc {
    display: none;
    color: @dark-text-color;
    font-size: 16px;
    line-height: 24px;
  }
}
