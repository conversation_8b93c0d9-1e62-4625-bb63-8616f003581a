.banner-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    opacity: 0.6;
    content: '';
  }

  .banner-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .banner-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 29%;
  }

  .banner-title {
    z-index: 1;
    width: fit-content;
    color: @dark-title-color;
    font-size: 32px;
    line-height: 45px;
    white-space: nowrap;
  }

  .banner-title-highlight {
    position: relative;
    padding: 10px;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        144deg,
        rgba(2, 140, 245, 90%) 0%,
        rgba(4, 251, 250, 90%) 100%
      );
      border-radius: @border-radius-base;
      transform: matrix(-1, 0, 0, 1, 0, 0);
      opacity: 0.3;
      content: '';
    }
  }

  .banner-line {
    width: 20%;
    height: 1px;
    margin: 20px 0 10px;
    background-color: #dedede;
  }

  .banner-subtitle {
    color: #111;
    font-size: 50px;
    line-height: 70px;
    letter-spacing: 2px;
    white-space: nowrap;
  }

  .banner-desc {
    margin: 90px 0 30px;
    color: @dark-text-color;
    font-size: 18px;
    line-height: 25px;
    white-space: nowrap;
    opacity: 0.9;
  }

  .banner-button-box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 20px;
  }

  .use-btn,
  .copy-link-btn,
  .download-trigger {
    display: flex;
    align-items: center;
    box-sizing: content-box;
    padding: 10px 34px;
    line-height: 22px;
    column-gap: 6px;
    border: none;
    box-shadow: 0 1px 2px rgba(103, 88, 229, 38%);
  }

  .use-btn,
  .copy-link-btn {
    color: #fff;
    background-image: linear-gradient(
        224.72deg,
        rgba(4, 251, 250, 90%) -1%,
        rgba(2, 140, 245, 90%) 100.5%
      ),
      linear-gradient(
        44.72deg,
        rgba(68, 129, 235, 20%) -0.5%,
        rgba(4, 190, 254, 20%) 101%
      );
  }

  .download-trigger {
    padding: 10px 24px;
    background-image: linear-gradient(
        236.28deg,
        rgba(68, 129, 235, 10%) -1.9%,
        rgba(4, 190, 254, 10%) 54.44%
      ),
      linear-gradient(
        236.45deg,
        rgba(162, 90, 255, 90%) 10.07%,
        rgba(82, 141, 255, 90%) 72.64%
      );
  }

  .gradient-btn {
    padding: 9px 29px;
    line-height: 22px;
    background-image: linear-gradient(to right, #f4f8ff, #f4f8ff),
      linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border-color: transparent;

    > span {
      display: inline-block;
      background-image: linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .arrow-download {
    margin-left: 8px;
  }

  .more-version {
    top: 44px;
    left: 0;
    padding-top: 8px;
  }

  .version-product {
    width: 100% !important;
    padding: 0 20px !important;

    &::before {
      content: unset !important;
    }
  }

  .download-button {
    display: flex;
    justify-content: space-between;
    width: 270px;
    height: 100%;
    margin: 14px 0;
    padding: 4px 10px;
    color: @dark-text-color;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    letter-spacing: 0.666667px;
  }

  .download-icon {
    width: 20px !important;
    height: 20px !important;
  }

  .banner-prompt {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    white-space: nowrap;
    background-image: linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .banner-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 66%;
    padding: 60px 0 40px;
  }

  .banner-image {
    width: 100%;
    margin-right: -8vw;
  }

  .banner-login {
    position: absolute;
    visibility: hidden;
    opacity: 0;
  }
}
