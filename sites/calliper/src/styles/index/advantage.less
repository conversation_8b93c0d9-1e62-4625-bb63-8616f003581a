.advantage-wrapper {
  padding: 120px 0;
  background-image: linear-gradient(0deg, #fbfbfb 0%, #fbfbfb 100%),
    linear-gradient(90deg, #2764ce 0%, #6561e9 100%);
  border-bottom: @border-base;

  .advantage-content {
    display: flex;
    justify-content: space-between;
    column-gap: 7%;
  }

  .content-left {
    width: 48%;
  }

  .content-right {
    width: 100%;
  }

  .content-list {
    padding-left: 25px;
    border-right: @border-base;
  }

  .list-item {
    position: relative;
    margin-bottom: 47px;
    padding-left: 0;

    &:last-child {
      margin-bottom: 0;
    }

    &::before {
      position: absolute;
      left: -18px;
      line-height: calc(
        var(--list-item-title-line-height) + var(--list-item-desc-line-height) +
          (var(--list-item-content-padding-top) * 2)
      );
      content: url('../../assets/images/circle-li.svg');
    }
  }

  .list-item-content {
    padding: var(--list-item-content-padding-top) 0;
    padding-right: 10px;
    cursor: pointer;
  }

  .list-item-content-active {
    position: relative;

    &::after {
      position: absolute;
      top: 0;
      left: calc(-1 * var(--list-item-offset-left));
      width: calc(100% + var(--list-item-offset-left));
      height: 100%;
      background-color: #20aaf51a;
      border-radius: 50px 0 0 50px;
      content: '';
    }

    .list-item-title {
      color: #20aaf5;
    }
  }

  .list-item-title {
    color: @dark-title-color;
    font-size: 20px;
    line-height: var(--list-item-title-line-height);
  }

  .list-item-desc {
    color: @dark-text-color;
    font-size: 16px;
    line-height: var(--list-item-desc-line-height);
  }

  .content-video {
    width: 100%;
    border: @border-base;
  }

  .advantage-content-mobile {
    display: none;

    .dropdown-toggle,
    .dropdown-menu {
      &.show {
        background-color: #fbfbfb;
      }
    }

    .dropdown-toggle {
      &::after {
        content: unset;
      }

      &.show {
        .list-item-title {
          &::after {
            transform: matrix(-0.71, -0.71, -0.71, 0.71, 0, 0);
          }
        }
      }
    }

    .dropdown-menu {
      position: static !important;
      padding: 0;
      border: none;
      transform: none !important;
    }

    .list-item-mobile {
      display: flex;
      flex-direction: column;
      padding: 20px 5px 30px;
      border-top: @border-base;
      row-gap: 10px;
    }

    .list-item-title {
      position: relative;
      display: flex;
      align-items: center;

      &::after {
        position: absolute;
        right: 0;
        width: 16px;
        height: 16px;
        margin-top: 3px;
        border: solid @dark-title-color;
        border-width: 2px 2px 0 0;
        transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
        content: '';
      }
    }

    .content-video {
      margin-bottom: 22px;
    }
  }
}
