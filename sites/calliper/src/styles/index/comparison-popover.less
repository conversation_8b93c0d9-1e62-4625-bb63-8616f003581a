.comparison-information-popover {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 236px;
  padding: 18px;
  border: @border-base;
  border-radius: @border-radius-base;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 12%);
  inset: 5px auto auto 0 !important;

  .information-icon {
    width: 16px;
    height: 16px;
    margin-bottom: 10px;
  }

  .information-text {
    color: @dark-text-color;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
  }
}

@media (max-width: @mini-screen-size) {
  .comparison-information-popover {
    width: 150px;
    padding: 10px;

    .information-text {
      font-size: 12px;
      line-height: 20px;
    }
  }
}
