.api-trial-wrapper {
  padding: 140px 0 300px;

  .api-trial-content {
    display: flex;
    justify-content: space-between;
    width: 75%;
    margin: 0 auto;
  }

  .content-right {
    margin-right: -100px;
  }

  .api-trial-img {
    width: 77%;
    margin-top: 25px;
  }

  .api-trial-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 27px;
  }

  .api-trial-title {
    margin-bottom: 50px;
    text-align: start;
  }

  .api-trial-desc {
    margin: 0 0 60px 20px;
  }

  .desc-item {
    list-style-type: circle;

    &::marker {
      color: #1665d8;
      font-size: 20px;
    }
  }

  .desc-text {
    color: @text-color;
    font-size: 20px;
    line-height: 36px;
    white-space: nowrap;
  }

  .api-trial-btn {
    padding: 9px 29px;
    line-height: 22px;
    background-image: linear-gradient(to right, #fff, #fff),
      linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border-color: transparent;
  }

  .api-trial-btn-text {
    background-image: linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
