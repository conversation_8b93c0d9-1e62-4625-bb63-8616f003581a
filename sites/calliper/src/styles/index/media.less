@media (max-width: 1800px) {
  .banner-wrapper {
    .banner-left {
      width: 32%;
    }

    .banner-image {
      width: 90%;
      margin-right: -4vw;
    }
  }
}

@media (max-width: @max-screen-size) {
  .demo-wrapper {
    .content-demo {
      width: 62vw;
      height: 36vw;
    }
  }
}

@media (max-width: @large-screen-size) {
  .banner-wrapper {
    .banner-left {
      width: 40%;
    }

    .banner-title {
      font-size: 30px;
      line-height: 40px;
    }

    .banner-line {
      margin: 10px 0;
    }

    .banner-subtitle {
      font-size: 45px;
      line-height: 50px;
    }

    .banner-desc {
      margin-top: 60px;
    }

    .banner-image {
      width: 95%;
      margin-right: -2vw;
    }
  }

  .advantage-wrapper {
    .advantage-content {
      column-gap: 5%;
    }

    .list-item {
      margin-bottom: 27px;
    }
  }

  .application-scenes-wrapper {
    .application-scenes-cards {
      width: 85%;
      row-gap: 3vw;
    }
  }

  .comparison-wrapper {
    .comparison-content {
      width: 85%;
    }

    .banner-title-product,
    .list-product-availability {
      width: 45%;
    }
  }

  .api-trial-wrapper {
    .api-trial-content {
      width: 85%;
    }

    .api-trial-img {
      width: 85%;
    }

    .content-right {
      margin-right: -25px;
    }
  }

  .scan-login {
    .scan-login-container {
      width: 350px;
      margin-top: -10px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .banner-wrapper {
    .banner-title {
      font-size: 26px;
    }

    .banner-title-highlight {
      padding: 6px;
    }

    .banner-subtitle {
      font-size: 34px;
    }

    .banner-desc {
      margin-top: 40px;
      font-size: 16px;
    }
  }

  .scan-login {
    .scan-login-container {
      width: 280px;
    }

    .scan-login-title {
      font-size: 16px;
      line-height: 22px;
    }

    .qr-desc {
      margin-bottom: 5px;
      font-size: 14px;
    }

    .qr-content {
      width: 150px;
      height: 150px;
      margin: 10px auto;
    }

    .wechat-tips-img {
      margin: 55px 0;
    }

    .user-info-wrap {
      padding: 20px 0;

      .user-personal-avatar-wrap {
        width: 80px;
        height: 80px;
      }
    }

    .redirect-button {
      width: 150px;
      max-width: none;
    }
  }

  .demo-wrapper {
    .content-bg {
      padding: 1.5vw 10vw 3vw;
    }

    .content-demo {
      width: 70vw;
      height: 40vw;
    }
  }

  .advantage-wrapper {
    .advantage-content {
      column-gap: 3%;
    }

    .list-item {
      margin-bottom: 15px;
    }
  }

  .application-scenes-wrapper {
    .application-scenes-cards {
      justify-content: space-evenly;
      width: 90%;
    }

    .application-scenes-card {
      width: 265px;
      height: 170px;
    }
  }

  .comparison-wrapper {
    .comparison-content {
      width: 90%;
    }

    .banner-title-product,
    .list-product-availability {
      width: 50%;
    }
  }

  .api-trial-wrapper {
    padding-bottom: 220px;

    .api-trial-content {
      width: 90%;
    }

    .api-trial-img {
      width: 90%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .banner-wrapper {
    .banner-title {
      font-size: 24px;
      line-height: 34px;
    }

    .banner-title-highlight {
      padding: 4px 6px;
    }

    .banner-subtitle {
      font-size: 26px;
      line-height: 36px;
    }

    .banner-desc {
      margin: 25px 0 15px;
      font-size: 13px;
    }
  }

  .scan-login {
    .scan-login-container {
      width: 280px;
    }

    .scan-login-title {
      font-size: 16px;
      line-height: 22px;
    }

    .qr-desc {
      font-size: 14px;
    }

    .qr-content {
      width: 150px;
      height: 150px;
      margin: 10px auto;
    }

    .wechat-tips-img {
      margin: 55px 0;
    }

    .user-info-wrap {
      padding: 20px 0;

      .user-personal-avatar-wrap {
        width: 80px;
        height: 80px;
      }
    }

    .redirect-button {
      width: 150px;
      max-width: none;
    }
  }

  .advantage-wrapper {
    padding: 70px 0 0;
    background-color: #fff;
    background-image: unset;

    .advantage-content {
      display: none;
    }

    .advantage-content-mobile {
      display: block;
    }
  }

  .application-scenes-wrapper {
    .application-scenes-cards {
      width: 100%;
    }

    .application-scenes-card {
      width: 214px;
      height: 138px;
      border-radius: 25px;
    }

    .content-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 14px;
    }

    .content-title {
      font-size: 20px;
      line-height: 36px;
    }

    .content-desc {
      font-size: 16px;
      line-height: 22px;
    }
  }

  .comparison-wrapper {
    .comparison-content {
      width: 100%;
    }

    .banner-title-product,
    .list-product-availability {
      width: 55%;
    }

    .banner-title-text {
      font-size: 22px;
    }

    .list-type-text {
      font-size: 16px;
      line-height: 22px;
    }
  }

  .api-trial-wrapper {
    .api-trial-content {
      flex-direction: column-reverse;
      width: 100%;
    }

    .api-trial-img {
      width: 100%;
      margin-top: 0;
    }

    .content-right {
      margin-right: 0;
      margin-bottom: 30px;
    }

    .api-trial-icon {
      margin-bottom: 17px;
    }

    .api-trial-desc {
      margin-bottom: 30px;
    }

    .api-trial-title {
      margin-bottom: 15px;
    }

    .desc-item {
      &::marker {
        font-size: 18px;
      }
    }

    .desc-text {
      font-size: 18px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .banner-wrapper {
    display: unset;

    &::after {
      height: 80%;
    }

    .banner-container {
      position: relative;
      flex-direction: column;
      align-items: center;
    }

    .banner-left {
      align-items: center;
    }

    .banner-right,
    .banner-left {
      width: 100%;
    }

    .banner-right {
      align-items: normal;
      justify-content: center;
      padding: 0;
    }

    .banner-title {
      margin-top: 60px;
    }

    .banner-title-highlight {
      padding: 10px 20px;
    }

    .banner-desc {
      margin: 30px 0 20px;
    }

    .banner-button-box {
      position: absolute;
      bottom: -70px;
    }

    .banner-button-box-login {
      bottom: -150px;
    }

    .banner-button-box-mobile {
      bottom: -100px;
      flex-direction: column;
      row-gap: 20px;
    }

    .gradient-btn {
      background-image: linear-gradient(to right, #fff, #fff),
        linear-gradient(319.57deg, #04fbfa 0%, #028cf5 157.86%);
    }

    .banner-image {
      width: 100%;
      margin-right: 0;
    }
  }

  .advantage-wrapper {
    padding-top: 60px;

    &.advantage-wrapper-mobile {
      padding-top: 200px;
    }

    .list-item-title {
      font-size: 16px;
      line-height: 22px;
    }

    .list-item-desc {
      font-size: 14px;
      line-height: 20px;
    }

    .advantage-content-mobile {
      .list-item-title {
        &::after {
          width: 10px;
          height: 10px;
        }
      }
    }
  }

  .demo-wrapper {
    padding: 140px 0 60px;
  }

  .application-scenes-wrapper {
    padding: 60px 0;

    .application-scenes-title {
      margin-bottom: 60px;
    }

    .content-title {
      font-size: 14px;
      line-height: 30px;
    }

    .content-desc {
      font-size: 14px;
      line-height: 20px;
    }
  }

  .comparison-wrapper {
    padding: 60px 0;

    .comparison-title,
    .comparison-banner {
      margin-bottom: 26px;
    }

    .comparison-lists {
      row-gap: 20px;
    }

    .comparison-list {
      padding: 20px 10px;
    }

    .list-title,
    .list-item {
      margin-bottom: 20px;
    }

    .banner-title-text {
      font-size: 14px;
      line-height: 20px;
    }

    .banner-title-calliper {
      padding: 4px 8px;
    }

    .list-title-text {
      font-size: 14px;
      line-height: 19px;
    }

    .list-type-text {
      font-size: 12px;
      line-height: 17px;
    }

    .availability-icon {
      width: 16px;
      height: 16px;
    }
  }

  .api-trial-wrapper {
    padding: 30px 0 120px;

    .api-trial-icon {
      width: 32px;
      height: 32px;
    }

    .desc-text {
      font-size: 14px;
      line-height: 36px;
    }
  }
}

@media (max-width: @least-screen-size) {
  .application-scenes-wrapper {
    padding-bottom: 0;

    .application-scenes-title {
      margin-bottom: 0;
    }

    .application-scenes-cards {
      position: relative;
      left: calc(-1 * var(--application-scenes-cards-offset-left));
      flex-wrap: nowrap;
      justify-content: normal;
      width: calc(100% + (var(--application-scenes-cards-offset-left) * 2));
      padding: 60px 0;
      overflow: auto;
      column-gap: 20px;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .application-scenes-card {
      flex-shrink: 0;

      &:first-child {
        margin-left: 20px;
      }

      &:last-child {
        margin-right: 20px;
      }
    }
  }

  .comparison-wrapper {
    .banner-title,
    .list-type {
      width: 42%;
    }

    .banner-title-product,
    .list-product-availability {
      width: 100%;
    }
  }

  .api-trial-wrapper {
    .api-trial-icon {
      width: 20px;
      height: 20px;
    }

    .desc-item {
      &::marker {
        font-size: 14px;
      }
    }

    .desc-text {
      font-size: 14px;
    }
  }
}
