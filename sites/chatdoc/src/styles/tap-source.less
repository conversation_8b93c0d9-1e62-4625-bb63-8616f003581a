.tap-source-wrapper {
  .row {
      position: relative;
  }
  .module-title{
    margin-top: 69px;
    span {
      background: linear-gradient(86deg, #627ae6 40.34%, #d6b0bc 71.07%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tap-source-swiper-content {
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(130deg, #000 1%, #222 114.3%);
    padding: 73px 35px;
    .tap-source-name {
      text-align: center;
      font-family: Poppins-blod;
      font-size: 32px;
      font-weight: 600;
      line-height: 38px;
      margin-top: 28px;
      background: linear-gradient(
        88deg,
        #fff 2.84%,
        rgba(255, 255, 255, 0.8) 76.02%
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tap-source-images-swiper {
    position: static;
  }
  .swiper-pagination {
    position: absolute;
    left: 0;
    bottom: 50px;
    width: 360px;
  }
}
