import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import CommonSwiper from '../../../../common/components/swiper/common-swiper';

import '../styles/tap-source.less';

const TapSource = () => {
  const { allTapSourceData } = useGlobalContext();
  console.log(allTapSourceData);
  return (
    <div className="tap-source-wrapper">
      <Container className="tap-source-container">
        <Row>
          <Col lg={6} md={12} className="tap-source-left">
            <h1 className="module-title">
              With <span>TapSource™</span> Answers and Origins Hold Hands.
            </h1>
          </Col>
          <Col lg={6} md={12} className="tap-source-right">
            <div className="tap-source-swiper-content">
              <CommonSwiper
                className="tap-source-images-swiper"
                swiperData={allTapSourceData}
                centeredSlides={true}
                paginationType="line"
                swiperSlideChildren={(item, index) => (
                  <div key={index}>
                    <GatsbyImage
                      image={item.image.childImageSharp.gatsbyImageData}
                      alt=""
                      className="tap-source-image"
                    />
                    <p className="tap-source-name">{item.name}</p>
                  </div>
                )}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default TapSource;
