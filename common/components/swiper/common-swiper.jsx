import React from 'react';
import PropTypes from 'prop-types';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper';
import 'swiper/css';
import 'swiper/css/pagination';
import './common-swiper.less';
import classnames from 'classnames';

const CommonSwiper = ({
  className,
  swiperData,
  swiperSlideChildren,
  initialSlide,
  slidesPerView,
  slidesPerGroup,
  spaceBetween,
  direction,
  centeredSlides,
  onSlideChange,
}) => {
  return (
    <Swiper
      modules={[Pagination, Autoplay]}
      initialSlide={initialSlide}
      slidesPerView={slidesPerView}
      slidesPerGroup={slidesPerGroup}
      spaceBetween={spaceBetween}
      direction={direction}
      centeredSlides={centeredSlides}
      onSlideChange={onSlideChange}
      loop
      pagination={{ clickable: true }}
      autoplay={{
        delay: 3000,
        disableOnInteraction: false,
      }}
      className={classnames({
        'common-swiper': true,
        [className]: className,
      })}>
      {swiperData.map((item, index) => (
        <SwiperSlide key={index}>{swiperSlideChildren(item)}</SwiperSlide>
      ))}
    </Swiper>
  );
};

CommonSwiper.defaultProps = {
  initialSlide: 0,
  slidesPerView: 1,
  direction: 'horizontal',
  centeredSlides: false,
  onSlideChange: () => {},
};

CommonSwiper.propTypes = {
  className: PropTypes.string,
  swiperData: PropTypes.array,
  swiperSlideChildren: PropTypes.func,
  initialSlide: PropTypes.number,
  slidesPerView: PropTypes.number,
  slidesPerGroup: PropTypes.number,
  spaceBetween: PropTypes.number,
  direction: PropTypes.oneOf(['horizontal', 'vertical']),
  centeredSlides: PropTypes.bool,
  onSlideChange: PropTypes.func,
};

export default CommonSwiper;
