import React from 'react';
import PropTypes from 'prop-types';
import { NavDropdown } from 'react-bootstrap';
import { getUrlWithProduct } from '../../../urls';
import ExtensionPng from '../../../assets/images/chatdoc/extension-icon.png'
import ChromeIcon from '../../../../common/assets/icons/chrome.svg';
import EdgeIcon from '../../../../common/assets/icons/edge.svg';
import './browser-dropdown.less';

const BROWSER_MENU_DATA = [
  {
    name: 'Chrome',
    icon: ChromeIcon,
    url: getUrlWithProduct(
      'other',
      'chatdocExtensionChrome',
    )
  },
  {
    name: 'Edge',
    icon: EdgeIcon,
    url: getUrlWithProduct('other', 'chatdocExtensionEdge')
  },
];

const BrowserDropDown = ({ handleCloseCollapse }) => {

  return (
    <NavDropdown
      title={<img src={ExtensionPng} alt="browser"/>}
      className="browser-dropdown"
      renderMenuOnMount>
      <div className="menu-content">
        {BROWSER_MENU_DATA.map((item, index) => (
          <a
            key={index}
            target="_blank"
            rel="noreferrer"
            className="menu-item"
            href={item.url}
            onClick={handleCloseCollapse}>
            <item.icon className="extension-icon" />
            {item.name}
          </a>
        ))}
      </div>
    </NavDropdown>
  );
};

BrowserDropDown.propTypes = {
  handleCloseCollapse: PropTypes.func,
};

export default BrowserDropDown;
