.header-wrapper {
  position: sticky;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  height: var(--header-height);
  background-color: #fff;

  &.shadow {
    box-shadow:
      0 -1px #fff,
      0 0.5rem 1rem rgba(0, 0, 0, 15%) !important;
  }

  .header-navbar {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
  }

  .navbar-main {
    display: flex;
    flex: auto;
    align-items: center;
    justify-content: flex-end;
  }

  .navbar-collapse {
    flex-grow: 0;
  }

  .nav-right {
    flex-direction: row;
    align-items: center;
  }

  .nav-link {
    margin-right: 30px;
    padding: 0 !important;
    color: @dark-title-color;
    font-size: 16px;
    font-family: Poppins-Medium;
    line-height: 24px;
    letter-spacing: -0.166667px;
    white-space: nowrap;

    &:hover {
      color: #6576db;
    }
  }

  .api-dropdown {
    &.nav-link {
      padding: 0 !important;
    }
  }
}

.v3-header-wrapper {
  background-color: #131313;
  .brand-name {
    color: #fff;
  }
  .navbar-main {
    justify-content: center;
  }
  .nav-link {
    color: rgba(255, 255, 255, 0.5);
    &:hover, &.active {
      color: #fff;
    }
  }
  .v3-header-btns {
    display: flex;
    align-items: center;
  }
}

@media (max-width: @middle-screen-size) {
  .header-wrapper {
    .nav-link {
      margin-right: 13px;
      font-size: 15px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .header-wrapper {
    .header-navbar {
      position: unset;
      flex-direction: row;
    }

    .navbar-main {
      flex: unset;
      margin-right: 20px;
      margin-left: auto;
    }

    .navbar-toggler {
      display: block;
      margin-right: 0;
      margin-left: auto;
      border: none;
      opacity: 0;

      &:focus {
        box-shadow: none;
      }
    }

    .navbar-collapse {
      position: absolute;
      top: var(--header-height);
      left: 0;
      display: block !important;
      width: 100vw;
      height: calc(100vh - var(--header-height));
      background-color: @navbar-collapse-bg;

      &:not(.show) {
        display: none !important;
      }
    }

    .navbar-dropdown {
      background-color: #fff;
      border-top: @border-base;
      border-bottom: @border-base;
    }

    .nav-right {
      flex-direction: column;
      align-items: unset;
    }

    .nav-link {
      margin: 0 auto !important;
      padding: 12px 0 !important;
    }

    .navbar-product-hunt {
      display: none;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .header-wrapper {
    --header-height: 65px;

    .navbar-main {
      margin-right: 0;
    }

    .navbar-toggler {
      margin-right: 20px;
      padding: 4px 0;
    }

    .nav-link {
      padding: 10px 20px !important;
      font-size: 14px;
    }
  }
}
