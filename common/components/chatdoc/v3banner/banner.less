.v3-banner-wrapper {
  background-color: transparent;
  background-image: none;
  padding: 180px 0 75px;

  .banner-row {
    align-items: center;
    .title-box {
      display: flex;
      align-items: center;
    }
    .banner-desc {
      color: rgba(255, 255, 255, 0.6);
      font-size: 26px;
      font-weight: 400;
      line-height: 28px;
      margin-top: 50px;
    }
    .banner-right {
      .video-box {
        width: 100%;
        height: 100%;
        position: relative;
        .video-bg {
          width: 100%;
          height: 100%;
          position: absolute;
          &::before {
            content: '';
            position: absolute;
            width: 171px;
            height: 185px;
            left: 0;
            top: 0;
            background: radial-gradient(
              ellipse at center,
              rgba(75, 183, 90, 0.2) 60%,
              transparent 100%
            );
            filter: blur(100px);
            opacity: 1;
            z-index: 0;
          }
          &::after {
            content: '';
            position: absolute;
            width: 89px;
            height: 186px;
            right: -45px;
            bottom: 85px;
            background: radial-gradient(
              ellipse at center,
              #627ae6 30%,
              transparent 100%
            );
            opacity: 0.3;
            filter: blur(50px);
            z-index: 0;
          }
          .video-bg-img {
            position: absolute;
            width: 100%;
            height: auto;
            z-index: 1;
          }
          .video-bg-blue-line {
            position: absolute;
            width: 1px;
            height: 117px;
            bottom: 100px;
            right: 0px;
            z-index: 1;
            background: linear-gradient(
              to bottom,
              rgba(98, 122, 230, 0) 0%,
              #627ae6 50%,
              rgba(98, 122, 230, 0) 100%
            );
          }
        }
      }
    }
  }
}
