import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './customer.less';

const Customer = () => {
  const { allCustomersData } = useGlobalContext();
  console.log(allCustomersData);
  return (
    <div className="customer-container">
      {allCustomersData.nodes.map((customer) => (
        <div className='customer-btn' key={customer.name}>
          <GatsbyImage
            image={customer.childImageSharp.gatsbyImageData}
            alt={customer.name}
            className="customer-image"
          />
        </div>
      ))}
    </div>
  );
};

export default Customer;
