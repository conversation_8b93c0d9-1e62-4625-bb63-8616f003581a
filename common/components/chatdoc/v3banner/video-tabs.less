.video-tabs-box {
  width: 100%;
  height: 548px;
  z-index: 2;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 38px 14px 14px;
  .video-tabs {
    display: flex;
    .video-tab-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.4);
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      font-size: 20px;
      padding: 12px 23px 5px;
      cursor: pointer;
      border: 1px transparent;
      &.active {
        background: #000;
        position: relative;
        top: 1px;
        color: rgba(255, 255, 255, 0.80);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid #000;
        border-radius: 10px 10px 0 0;
      }
      &:hover {
        color: rgba(255, 255, 255, 0.80);
      }
    }
  }
  .video-player-box {
    width: 100%;
    flex: 1;
    border-radius: 0 16px 16px 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: #000;
    .banner-video {
      width: 100%;
      height: 100%;
      border-radius: 0 16px 16px 16px;
    }
  }
}
