import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Container, Form, Card } from 'react-bootstrap';
import PricingCard from './pricing-card';
import { ScrollToElement } from '../../scroll/pageScroll/page-scroll';
import { getMembershipPackagesApi } from '../../../api/chatdoc/goods';
import {
  PRICING_TYPE_DATA,
  PRICING_DESC_UNIT,
  MEMBERSHIP_ID,
  MEMBERSHIP_DAYS,
  PRICING_ITEM_LIST,
} from '../../../data/chatdoc/data';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import classnames from 'classnames';
import './pricing.less';

const Pricing = () => {
  const { pricingData } = useGlobalContext();
  const pricingSwithRef = useRef();
  const [isChecked, setChecked] = useState(true);

  const pricingDataWithoutProPlus = useMemo(() => {
    return pricingData.filter(
      (item) => item.alias !== PRICING_TYPE_DATA.PRO_PLUS,
    );
  }, [pricingData]);

  const [membershipPackagesData, setMembershipPackagesData] = useState();
  const [currentPricingData, setCurrentPricingData] = useState(
    pricingDataWithoutProPlus,
  );

  const getMembershipPackagesData = async () => {
    try {
      const membershipPackages = await getMembershipPackagesApi();
      setMembershipPackagesData(
        membershipPackages.filter((pkg) => !pkg.is_deprecated),
      );
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    getMembershipPackagesData();
  }, []);

  const isMonthlyProPackage = ({ membership_id, days }) => {
    return (
      membership_id === MEMBERSHIP_ID.PRO && days === MEMBERSHIP_DAYS.MONTHLY
    );
  };

  const isAnnualProPackage = ({ membership_id, days }) => {
    return (
      membership_id === MEMBERSHIP_ID.PRO && days === MEMBERSHIP_DAYS.ANNUALLY
    );
  };

  const allPricingData = useMemo(() => {
    if (membershipPackagesData) {
      let allPricingDataList = [];
      membershipPackagesData.forEach((membershipItem) => {
        const pricingItem = pricingDataWithoutProPlus.find(
          (item) => item.alias === membershipItem.alias,
        );
        Object.assign(membershipItem, pricingItem);
        allPricingDataList.push(membershipItem);
      });
      return allPricingDataList;
    }
  }, [membershipPackagesData, pricingDataWithoutProPlus]);

  useEffect(() => {
    if (allPricingData) {
      let callbackFn;
      let currentPricingDataList = [];
      if (isChecked) {
        callbackFn = (item) => !isMonthlyProPackage(item);
      } else {
        callbackFn = (item) => !isAnnualProPackage(item);
      }
      currentPricingDataList = allPricingData.filter(callbackFn);
      setCurrentPricingData(currentPricingDataList);
    }
  }, [isChecked, allPricingData]);

  return (
    <div className="pricing-wrapper">
      <Container>
        <h3 className="common-title pricing-title">Plans and Pricing</h3>
        <p className="common-subtitle pricing-subtitle">
          Start for free, upgrade for more.
        </p>
        <div className="pricing-switch">
          <span
            className={classnames({
              'switch-time': true,
              'pricing-highlight': !isChecked,
            })}>
            {PRICING_DESC_UNIT.FILES_30_DAYS_UNIT.slice(1)}
          </span>
          <Form.Check
            type="switch"
            ref={pricingSwithRef}
            className="switch-check"
            defaultChecked={true}
            onChange={() => {
              setChecked(pricingSwithRef.current.checked);
            }}
          />
          <span
            className={classnames({
              'switch-time': true,
              'pricing-highlight': isChecked,
            })}>
            {PRICING_DESC_UNIT.FILES_360_DAYS_UNIT.slice(1)}
          </span>
        </div>
        <div className="pricing-cards-container">
          <div className="pricing-cards">
            <Card className="pricing-card-item">
              <div className="card-title"></div>
              <div className="card-desc">
                {PRICING_ITEM_LIST.map((pricingItem, pricingIndex) => (
                  <div key={pricingIndex} className="desc-item">
                    {pricingItem}
                  </div>
                ))}
              </div>
            </Card>
            {currentPricingData.map((item, index) => (
              <PricingCard key={index} item={item} isChecked={isChecked} />
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ScrollToElement(Pricing, 'pricing');
