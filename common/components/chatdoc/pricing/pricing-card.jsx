import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Card } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import ProjectButton from '../button/project-button';
import {
  PRICING_TYPE_DATA,
  PRICING_DESC_UNIT,
} from '../../../data/chatdoc/data';
import RightSvg from '../../../assets/icons/chatdoc/right.svg';
import classnames from 'classnames';

const PricingCard = ({ item, isChecked }) => {
  const isProPackage = useMemo(() => {
    return item.alias === PRICING_TYPE_DATA.PRO;
  }, [item]);

  return (
    <Card
      className={classnames({
        'pricing-card-item': true,
        'pricing-card-pro': isProPackage,
        'pricing-card-free': !isProPackage,
      })}>
      <div className="card-title">
        <div className="title-text">
          <h5 className="title-text-alias">{item.alias}</h5>
          {item.icon && (
            <GatsbyImage
              image={item.icon.childImageSharp.gatsbyImageData}
              alt={`${item.alias}-icon`}
              className="title-icon"
            />
          )}
        </div>
        <div className="title-price">
          <span className="price-value">${item.price}</span>
          {isProPackage && (
            <span className="price-time">
              {isChecked
                ? PRICING_DESC_UNIT.FILES_360_DAYS_UNIT
                : PRICING_DESC_UNIT.FILES_30_DAYS_UNIT}
            </span>
          )}
        </div>
        <ProjectButton
          className="card-btn"
          text={item.buttonTitle}
          params={item.params}
        />
      </div>
      <div className="card-desc">
        <div className="desc-item">
          {isProPackage ? item.files_per_month : item.files_per_day}
          {isProPackage
            ? PRICING_DESC_UNIT.FILES_30_DAYS_UNIT
            : PRICING_DESC_UNIT.FILES_UNIT}
          {isProPackage ? '' : `, ${PRICING_DESC_UNIT.FILES_TOTAL}`}
        </div>
        <div className="desc-item">
          {item.questions_per_day}
          {PRICING_DESC_UNIT.FILES_UNIT}
          {isProPackage ? '' : `, ${PRICING_DESC_UNIT.QUESTIONS_TOTAL}`}
        </div>
        <div className="desc-item">
          {isProPackage ? 'Unlimited' : item.pages}
        </div>
        <div className="desc-item">
          {item.size / 1024 / 1024}
          &nbsp;
          {PRICING_DESC_UNIT.FILE_SIZE_UNIT}
        </div>
        <div className="desc-item">
          {item.ocr_pages_per_month
            ? `${item.ocr_pages_per_month}${PRICING_DESC_UNIT.FILES_30_DAYS_UNIT}`
            : '-'}
        </div>
        <div className="desc-item">
          {item.supportGPT4 ? <RightSvg className="right-icon" /> : '-'}
        </div>
        <div className="desc-item">
          {item.supportFormulaRecognition ? (
            <RightSvg className="right-icon" />
          ) : (
            '-'
          )}
        </div>
        <div className="desc-item">
          {isProPackage ? 'Unlimited' : item.files_per_collection}
        </div>
        <div className="desc-item">
          {item.supportType ? item.supportType : '-'}
        </div>
      </div>
    </Card>
  );
};

PricingCard.propTypes = {
  item: PropTypes.object,
  isChecked: PropTypes.bool,
};

export default PricingCard;
