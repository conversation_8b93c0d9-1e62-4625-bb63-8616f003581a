import { arrayBufferToFile, fileToArrayBuffer } from '../index';

let PDFLib = null;

const loadPdfLib = async () => {
  if (!PDFLib) {
    PDFLib = await import('pdf-lib');
  }
};

export const loadPdf = async (file) => {
  try {
    await loadPdfLib();
    const { PDFDocument } = PDFLib;
    const fileBytes = await fileToArrayBuffer(file);
    return await PDFDocument.load(fileBytes);
  } catch (e) {
    const { message } = e;
    if (message.includes('encrypted')) {
      throw new Error(
        "Your file is protected by a password so we can't open it.",
      );
    } else {
      throw new Error('Sorry, but your document seems to be malformed.');
    }
  }
};

export const splitPdf = async (file, maxPages = 1) => {
  await loadPdfLib();
  const { PDFDocument } = PDFLib;
  const pdfDoc = await loadPdf(file);
  const newPdfDoc = await PDFDocument.create();

  for (let i = 0; i < maxPages; i++) {
    const [copiedPage] = await newPdfDoc.copyPages(pdfDoc, [i]);
    newPdfDoc.addPage(copiedPage);
  }

  const newPdfBytes = await newPdfDoc.save();

  return arrayBufferToFile(newPdfBytes, file.name, file.type);
};
